export default function UnregisteredPatient() {
  return (
    <div className="w-full h-full flex bg-white flex-col">
      <div className="flex justify-between items-center border-b-2 border-gray-400 p-4">
        <h1 className="text-lg font-bold">Unregistered Patient</h1>
      </div>
      <div className="border border-gray-200 rounded-lg h-full p-4 bg-[#FDFDFE]">
        <div className="flex flex-col items-center justify-center h-full">
          <img className='h-16' src="/images/EmptyDocuments.png" alt="" />
          <p className="text-gray-500 mt-4">Unregistered Patient functionality coming soon</p>
        </div>
      </div>
    </div>
  )
}

import React, { useState } from 'react';
import { Search, ChevronDown } from 'lucide-react';

export default function PrescriptionBillingForm() {
  const [formData, setFormData] = useState({
    name: '',
    age: '',
    sex: 'Select',
    phoneNumber: '',
    doctorName: '',
    drugMaterial: 'Select',
    batchNumber: '',
    qty: '',
    expiryDate: '',
    discPercentage: ''
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* Patient Detail Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide">
            PATIENT DETAIL
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-2">Age</label>
              <input
                type="text"
                placeholder="Enter"
                value={formData.age}
                onChange={(e) => handleInputChange('age', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-2">Sex</label>
              <div className="relative">
                <select
                  value={formData.sex}
                  onChange={(e) => handleInputChange('sex', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white text-gray-400"
                >
                  <option>Select</option>
                  <option>Male</option>
                  <option>Female</option>
                  <option>Other</option>
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Phone number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Doctor's Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter"
                value={formData.doctorName}
                onChange={(e) => handleInputChange('doctorName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Ready for Billing Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wide">
            READY FOR BILLING
          </h3>
        </div>
        
        <div className="p-6">
          {/* Search & Add Drugs Section */}
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <h4 className="text-sm font-medium text-gray-600 mb-4 uppercase tracking-wide">
              SEARCH & ADD DRUGS
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
              <div>
                <label className="block text-sm text-gray-700 mb-2">
                  Drug/material <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <select
                    value={formData.drugMaterial}
                    onChange={(e) => handleInputChange('drugMaterial', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white text-gray-400"
                  >
                    <option>Select</option>
                    <option>Paracetamol</option>
                    <option>Aspirin</option>
                    <option>Ibuprofen</option>
                  </select>
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-2">
                  Batch Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter"
                  value={formData.batchNumber}
                  onChange={(e) => handleInputChange('batchNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-2">
                  QTY <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter"
                  value={formData.qty}
                  onChange={(e) => handleInputChange('qty', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-2">
                  Expiry date <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="MM/YYYY"
                  value={formData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-2">
                  Disc % <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter"
                  value={formData.discPercentage}
                  onChange={(e) => handleInputChange('discPercentage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
                />
              </div>
            </div>
            
            <div className="mb-4">
              <input
                type="text"
                placeholder="Enter quantity & packaging to check qty available for dipact"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400"
              />
            </div>
            
            <div className="flex gap-3">
              <button className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                Add
              </button>
              <button className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                Reset
              </button>
            </div>
          </div>

          {/* Drug Table */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            {/* Table Header */}
            <div className="bg-cyan-50 border-b border-gray-200">
              <div className="grid grid-cols-7 gap-4 px-4 py-3">
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">DRUG</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">QTY</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">BATCH NUMBER</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">EXP DATE</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">MRP(₹)</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">DISC %</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">GST%</div>
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">AMOUNT (₹)</div>
              </div>
            </div>
            
            {/* Empty State */}
            <div className="flex flex-col items-center justify-center py-16 px-6">
              {/* Document Icon */}
              <div className="relative mb-4">
                <div className="w-12 h-16 bg-white border-2 border-teal-400 rounded-lg relative">
                  {/* Document lines */}
                  <div className="space-y-1 p-2">
                    <div className="h-0.5 bg-teal-400 rounded w-6"></div>
                    <div className="h-0.5 bg-teal-400 rounded w-4"></div>
                    <div className="h-0.5 bg-teal-400 rounded w-5"></div>
                    <div className="h-0.5 bg-teal-400 rounded w-3"></div>
                  </div>
                </div>
                {/* Second document behind */}
                <div className="absolute -right-1 -top-1 w-12 h-16 bg-white border-2 border-teal-400 rounded-lg -z-10"></div>
                {/* Decorative dots */}
                <div className="absolute -left-3 top-1 w-1.5 h-1.5 bg-cyan-200 rounded-full"></div>
                <div className="absolute -left-1 -top-1 w-1 h-1 bg-cyan-300 rounded-full"></div>
                <div className="absolute -right-4 top-3 w-1 h-1 bg-cyan-200 rounded-full"></div>
                <div className="absolute -right-2 -top-1 w-0.5 h-0.5 bg-cyan-300 rounded-full"></div>
                <div className="absolute -left-4 top-6 w-0.5 h-0.5 bg-cyan-200 rounded-full"></div>
                <div className="absolute right-4 top-8 w-1.5 h-1.5 bg-cyan-200 rounded-full"></div>
                <div className="absolute right-5 top-4 w-0.5 h-0.5 bg-cyan-300 rounded-full"></div>
                <div className="absolute -left-2 top-8 w-1 h-1 bg-cyan-300 rounded-full"></div>
              </div>

              {/* Empty state text */}
              <p className="text-gray-500 text-sm mb-8">Add drug/medicine to see here</p>
            </div>
            
            {/* Total Section */}
            <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
              <div className="flex justify-end gap-8 text-sm">
                <div className="text-gray-600">
                  Total Items: <span className="font-medium text-gray-900">00</span>
                </div>
                <div className="text-gray-600">
                  Total Amount: <span className="font-medium text-gray-900">₹00.00</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
