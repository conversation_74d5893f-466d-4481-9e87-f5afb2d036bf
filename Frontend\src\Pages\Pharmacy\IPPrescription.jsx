export default function IPPrescription() {
  return (
    <div className="w-full h-full flex bg-white flex-col">
      <div className="flex justify-between items-center border-b-2 border-gray-400 p-4">
        <h1 className="text-lg font-bold">IP Prescription</h1>
      </div>
      <div className="border border-gray-200 rounded-lg h-full p-4 bg-[#FDFDFE]">
        <div className="flex flex-col items-center justify-center h-full">
          <img className='h-16' src="/images/EmptyDocuments.png" alt="" />
          <p className="text-gray-500 mt-4">IP Prescription functionality coming soon</p>
        </div>
      </div>
    </div>
  )
}
import React, { useState } from 'react';
import { Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';

export default function PatientAdmissionTable() {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 30;

  const patientData = [
    { mrn: '12345432343', ipNumber: '12345432343', name: '<PERSON><PERSON><PERSON> <PERSON>', age: '25Y', gender: 'Male', admittedBy: 'Dr. Arnav Sharma', bed: 'Room 11 - Bed 4' },
    { mrn: '**********', ipNumber: '6543', name: 'Priya Desai', age: '54Y', gender: 'Male', admittedBy: 'Dr. Rohan Kumar', bed: 'Room 24 - Bed 6' },
    { mrn: '**********', ipNumber: '**********', name: 'Rohan Patel', age: '98Y', gender: 'Female', admittedBy: 'Dr. Aryan Patel', bed: 'Room 24 - Bed 2' },
    { mrn: '**********', ipNumber: '**********', name: 'Ananya Gupta', age: '32Y', gender: 'Male', admittedBy: 'Dr. Nikita Joshi', bed: 'Room 15 - Bed 1' },
    { mrn: '**********', ipNumber: '76543', name: 'Vikram Singh', age: '76Y', gender: 'Male', admittedBy: 'Dr. Ananya Reddy', bed: 'Room 18 - Bed 3' },
    { mrn: '**********', ipNumber: '**********', name: 'Nisha Mehta', age: '44Y', gender: 'Male', admittedBy: 'Dr. Vikram Mehta', bed: 'Room 20 - Bed 5' },
    { mrn: '**********', ipNumber: '**********', name: 'Sonia Choudhary', age: '21Y', gender: 'Female', admittedBy: 'Dr. Sahil Kapoor', bed: 'Room 14 - Bed 7' },
    { mrn: '1111111111', ipNumber: '34567', name: 'Aarav Kumar', age: '63Y', gender: 'Male', admittedBy: 'Dr. Naina Gupta', bed: 'Room 22 - Bed 8' },
    { mrn: '3333333333', ipNumber: '3333333333', name: 'Jiya Verma', age: '89Y', gender: 'Male', admittedBy: 'Dr. Aisha Khan', bed: 'Room 17 - Bed 9' },
    { mrn: '4444444444', ipNumber: '76543', name: 'Rajat Kapoor', age: '70Y', gender: 'Female', admittedBy: 'Dr. Yash Malhotra', bed: 'Room 19 - Bed 10' },
    { mrn: '6666666666', ipNumber: '6666666666', name: 'Ishaan Khanna', age: '12Y', gender: 'Male', admittedBy: 'Dr. Arjun Mehta', bed: 'Room 12 - Bed 11' },
    { mrn: '**********', ipNumber: '6578909876', name: 'Mira Shah', age: '57Y', gender: 'Male', admittedBy: 'Dr. Anushka Patel', bed: 'Room 13 - Bed 12' },
    { mrn: '9999999999', ipNumber: '9999999999', name: 'Aisha Bhatia', age: '30Y', gender: 'Male', admittedBy: 'Dr. Riya Deshmukh', bed: 'Room 16 - Bed 13' },
    { mrn: '**********', ipNumber: '**********', name: 'Pooja Rao', age: '68Y', gender: 'Female', admittedBy: 'Dr. Priya Sharma', bed: 'Room 21 - Bed 14' },
    { mrn: '**********', ipNumber: '**********', name: 'Pooja Rao', age: '42Y', gender: 'Female', admittedBy: 'Dr. Priya Sharma', bed: 'Room 23 - Bed 15' }
  ];

  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 6;
    
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => setCurrentPage(currentPage - 1)}
          className="w-8 h-8 flex items-center justify-center rounded border border-gray-300 bg-white hover:bg-gray-50"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
      );
    }
    
    for (let i = 1; i <= Math.min(maxVisiblePages, totalPages); i++) {
      pages.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`w-8 h-8 flex items-center justify-center rounded text-sm font-medium ${
            currentPage === i
              ? 'bg-blue-500 text-white'
              : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          {i}
        </button>
      );
    }
    
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => setCurrentPage(currentPage + 1)}
          className="w-8 h-8 flex items-center justify-center rounded border border-gray-300 bg-white hover:bg-gray-50"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      );
    }
    
    return pages;
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4">
      {/* Search and Filter Bar */}
      <div className="flex items-center gap-4 mb-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="IP number"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors">
          <Filter className="w-4 h-4" />
          Filter
        </button>
      </div>

      {/* Table Container */}
      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white">
        {/* Table Header */}
        <div className="bg-cyan-50 border-b border-gray-200">
          <div className="grid grid-cols-7 gap-4 px-6 py-3">
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              MRN
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              IP NUMBER
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              NAME
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              AGE
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              GENDER
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              ADMITTED BY
            </div>
            <div className="text-sm font-medium text-gray-700 uppercase tracking-wide">
              BED
            </div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {patientData.map((patient, index) => (
            <div key={index} className="grid grid-cols-7 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors">
              <div className="text-sm text-gray-900">{patient.mrn}</div>
              <div className="text-sm text-gray-900">{patient.ipNumber}</div>
              <div className="text-sm text-gray-900">{patient.name}</div>
              <div className="text-sm text-gray-900">{patient.age}</div>
              <div className="text-sm text-gray-900">{patient.gender}</div>
              <div className="text-sm text-gray-900">{patient.admittedBy}</div>
              <div className="text-sm text-gray-900">{patient.bed}</div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="bg-white px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center gap-1">
              {renderPageNumbers()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}