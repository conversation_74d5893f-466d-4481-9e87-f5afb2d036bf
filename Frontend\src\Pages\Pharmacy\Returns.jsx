export default function Returns() {
  return (
    <div className="w-full h-full flex bg-white flex-col">
      <div className="flex justify-between items-center border-b-2 border-gray-400 p-4">
        <h1 className="text-lg font-bold">Returns</h1>
      </div>
      <div className="border border-gray-200 rounded-lg h-full p-4 bg-[#FDFDFE]">
        <div className="flex flex-col items-center justify-center h-full">
          <img className='h-16' src="/images/EmptyDocuments.png" alt="" />
          <p className="text-gray-500 mt-4">Returns functionality coming soon</p>
        </div>
      </div>
    </div>
  )
}


import { useState } from "react";

export default function BillReturnSearch() {
  const [billNumber, setBillNumber] = useState("");

  const handleSearch = () => {
    if (billNumber.trim()) {
      // Handle search logic here
      console.log("Searching for bill:", billNumber);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="border border-gray-300 rounded-lg p-6 bg-gray-50">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Search for a Bill to Begin Return
        </h2>
        <p className="text-gray-600 mb-6">
          Submit the bill number to fetch the items eligible for return. Make sure the patient has the original bill for accurate tracking.
        </p>
        
        <div className="flex gap-3 items-end">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Bill number"
              value={billNumber}
              onChange={(e) => setBillNumber(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-500"
            />
          </div>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-gray-400 text-white rounded-md hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Search
          </button>
        </div>
      </div>
    </div>
  );
}